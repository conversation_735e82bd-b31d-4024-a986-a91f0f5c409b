<script setup lang="ts">
  import { onMounted, reactive, ref, watch } from "vue";
  import { closeToast, showLoadingToast } from "vant";
  import "vant/lib/toast/style";
  import idFrontImg from "@/assets/img/id-front.png";
  import idBackImg from "@/assets/img/id-back.png";
  import {
    GenderOptions,
    MaritalStatusOptions,
    NationOptions,
    PoliticalStatusOptions
  } from "@/enums/form-enum";
  import { citizenIdCardApi, getBasicInfo, idAndBasicInfo } from "@/api/hrm";
  import { jsonToFormData } from "@/utils/object-to-formData";
  import { deepClone } from "@/utils/deep-clone";
  import { getFileUrl } from "@/utils/get-file-url";

  // Steps and user info
  const infoStep = useInfoStep();
  const { userInfo } = storeToRefs(infoStep);

  // UI state
  const isShow = ref(false);
  const btnLoading = ref(false);
  const showMaritalPicker = ref(false);
  const showPoliticalPicker = ref(false);

  // Form model
  const formModel = reactive<Api.User.IdAndBasicInfoRespVO>({
    idFrontImg: [],
    idBackImg: [],
    electronicPhotoFile: [],
    backIdCardId: null!,
    birthday: null!,
    currentResidence: null!,
    electronicPhotoId: null!,
    email: null!,
    ethnicity: null!,
    frontIdCardId: null!,
    gender: null!,
    genderText: "",
    ethnicityText: "",
    hobbies: null!,
    householdRegistration: null!,
    id: null!,
    idNumber: null!,
    maritalStatus: null!,
    maritalStatusText: "",
    name: null!,
    phoneNumber: null!,
    politicalStatus: null!,
    politicalStatusText: ""
  });

  // Picker columns
  const maritalStatusColumns = MaritalStatusOptions;
  const politicalStatusColumns = PoliticalStatusOptions;

  // Handle pickers confirm
  function onConfirm({ selectedValues, selectedOptions }: any) {
    formModel.maritalStatus = selectedValues[0];
    formModel.maritalStatusText = selectedOptions[0]?.text || "";
    showMaritalPicker.value = false;
  }
  function onPoliticalConfirm({ selectedValues, selectedOptions }: any) {
    formModel.politicalStatus = selectedValues[0];
    formModel.politicalStatusText = selectedOptions[0]?.text || "";
    showPoliticalPicker.value = false;
  }

  // Watch for ID uploads to trigger OCR
  watch(
    () => [formModel.idFrontImg.length, formModel.idBackImg.length],
    async ([f, b]) => {
      if (f && b) citizenIdCard();
    }
  );

  // OCR API
  async function citizenIdCard() {
    showLoadingToast({
      message: "正在识别证件...",
      forbidClick: true,
      loadingType: "spinner",
      duration: 0
    });
    try {
      const list = ["idFrontImg", "idBackImg"].flatMap((key) =>
        formModel[key].map((i: any) => ({
          businessType: i.businessType,
          kind: i.kind,
          file: i.file
        }))
      );
      const data = await citizenIdCardApi(jsonToFormData({ fileList: list }));
      Object.assign(data, {
        ethnicityText: NationOptions.find((i) => i.value === data.ethnicity)?.text || "",
        genderText: GenderOptions.find((i) => i.value === data.gender)?.text || ""
      });
      infoStep.IdSet(data.id);
      Object.assign(formModel, data);
      isShow.value = true;
    } catch (e) {
      console.error(e);
    } finally {
      closeToast();
    }
  }

  async function handleGetBasicInfo() {
    const data = await getBasicInfo(userInfo.value.id);
    data.electronicPhotoFile = [
      {
        url: getFileUrl(data.electronicPhotoId),
        businessType: 0,
        file: File,
        kind: 0
      }
    ];
    Object.assign(data, {
      ethnicityText: NationOptions.find((i) => i.value === data.ethnicity)?.text || "",
      genderText: GenderOptions.find((i) => i.value === data.gender)?.text || "",
      maritalStatusText:
        MaritalStatusOptions.find((i) => i.value === data.maritalStatus)?.text || "",
      politicalStatusText:
        PoliticalStatusOptions.find((i) => i.value === data.politicalStatus)?.text || ""
    });
    Object.assign(formModel, data);
    isShow.value = true;
  }

  onMounted(() => {
    if (userInfo.value.id) handleGetBasicInfo();
  });

  // 提交
  async function handleSubmit() {
    btnLoading.value = true;
    try {
      const payload = deepClone(formModel);
      payload.electronicPhotoFile = payload.electronicPhotoFile[0].file;
      await idAndBasicInfo(formModel.id, jsonToFormData(payload));
      infoStep.stepSet(2);
    } catch (e) {
      console.error(e);
    } finally {
      btnLoading.value = false;
    }
  }
</script>

<template>
  <div class="w-full min-h-screen bg-white flex flex-col">
    <div class="flex-1 px-2 pt-6 pb-10">
      <van-form @submit="handleSubmit">
        <div v-if="!isShow" class="space-y-8">
          <div
            v-for="(item, index) in [
              { model: 'idFrontImg', img: idFrontImg, kind: 2, label: '身份证人像面' },
              { model: 'idBackImg', img: idBackImg, kind: 3, label: '身份证国徽面' }
            ]"
            :key="index"
            class="flex flex-col items-center"
          >
            <div class="w-[300px] h-[180px]">
              <UploadIdImg
                v-model="formModel[item.model]"
                :width="300"
                :height="180"
                :businessType="1"
                :kind="item.kind"
              >
                <van-image fit="contain" :src="item.img" class="w-full h-full object-cover" />
              </UploadIdImg>
            </div>
            <div class="mt-3 text-gray-500 text-sm">上传{{ item.label }}</div>
          </div>
        </div>

        <!-- Form Fields -->
        <div v-else>
          <van-cell-group inset>
            <van-field
              v-for="field in [
                { key: 'name', label: '姓名', readonly: true },
                { key: 'genderText', label: '性别', readonly: true },
                { key: 'ethnicityText', label: '民族', readonly: true },
                { key: 'birthday', label: '出生日期', readonly: true },
                { key: 'idNumber', label: '身份证号', readonly: true },
                {
                  key: 'householdRegistration',
                  label: '住址',
                  type: 'textarea',
                  rows: 2,
                  readonly: true
                }
              ]"
              :key="field.key"
              v-model="formModel[field.key]"
              :label="field.label"
              :readonly="field.readonly || false"
              :type="field.type"
              :rows="field.rows"
              :placeholder="`请填写${field.label}`"
              :required="true"
            />
            <van-field
              v-model="formModel.phoneNumber"
              name="phone"
              type="number"
              label="手机号"
              placeholder="请填写手机号"
              label-width="60"
              :required="true"
              :rules="[{ required: true, message: '请填写手机号' }]"
            />
            <van-field
              v-model="formModel.email"
              name="email"
              label="邮箱"
              placeholder="请填写邮箱"
              label-width="40"
              :required="true"
              :rules="[
                { required: true, message: '请填写邮箱' },
                {
                  pattern: /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/,
                  message: '请输入正确的邮箱地址'
                }
              ]"
            />
            <van-field
              v-model="formModel.currentResidence"
              name="currentResidence"
              type="textarea"
              rows="2"
              label="现居住地址"
              placeholder="请填写现居住地址"
              label-width="90"
              :required="true"
              :rules="[{ required: true, message: '请填写现居住地址' }]"
            />
            <van-field
              v-model="formModel.maritalStatusText"
              readonly
              is-link
              label="婚姻状况"
              placeholder="请选择婚姻状况"
              @click="showMaritalPicker = true"
            />
            <van-field
              v-model="formModel.politicalStatusText"
              readonly
              is-link
              label="政治面貌"
              placeholder="请选择政治面貌"
              @click="showPoliticalPicker = true"
            />
            <van-field
              v-model="formModel.hobbies"
              label="特长/爱好"
              placeholder="请填写特长/爱好"
            />

            <van-field label="一寸电子照片" label-align="top" :required="true">
              <template #input>
                <UploadImg v-model="formModel.electronicPhotoFile" :businessType="1" :kind="4" />
                <div class="text-xs text-gray-500">用于制作工牌</div>
              </template>
            </van-field>
          </van-cell-group>

          <div class="p-2 flex justify-center">
            <van-button
              block
              round
              size="large"
              :loading="btnLoading"
              color="linear-gradient(90deg, #3b82f6 0%, #2563eb 100%)"
              style="box-shadow: 0 4px 10px rgba(37, 99, 235, 0.3)"
              class="!h-10"
              native-type="submit"
            >
              下一步
            </van-button>
          </div>
        </div>
      </van-form>
    </div>

    <!-- Pickers -->
    <van-popup v-model:show="showMaritalPicker" round position="bottom" destroy-on-close>
      <van-picker
        :columns="maritalStatusColumns"
        @confirm="onConfirm"
        @cancel="showMaritalPicker = false"
      />
    </van-popup>
    <van-popup v-model:show="showPoliticalPicker" round position="bottom" destroy-on-close>
      <van-picker
        :columns="politicalStatusColumns"
        @confirm="onPoliticalConfirm"
        @cancel="showPoliticalPicker = false"
      />
    </van-popup>
  </div>
</template>

<style scoped>
  /* 可根据需求调整样式 */
</style>
