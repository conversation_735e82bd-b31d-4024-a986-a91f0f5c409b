<script setup lang="ts">
  import { getDictDataOptionsApi } from "@/api/system";
  import { transformToTextValue } from "@/utils/common";
  import dayjs from "dayjs";
  import { closeToast, showFailToast, showLoadingToast } from "vant";
  import { deepClone } from "@/utils/deep-clone";
  import { getTrainCertificate, putTrainAndCertificates } from "@/api/hrm";
  import { jsonToFormData } from "@/utils/object-to-formData";

  const infoStep = useInfoStep();
  const { userInfo } = storeToRefs(infoStep);
  const englishColumns = ref<System.Common.dictOptionArray>([]);
  const driverColumns = ref<System.Common.dictOptionArray>([]);
  const computerColumns = ref<System.Common.dictOptionArray>([]);
  const formModel = reactive<Api.User.UpdateTrainingAndCertificatesReqVO[]>([
    {
      certificateFile: [],
      certificateTime: null!,
      certificateLevel: null!,
      certificateLevelText: null!,
      certificateName: null!,
      certificateType: null!,
      certificateTypeText: null!,
      trainName: null!,
      trainTime: "",
      trainBegin: "",
      trainEnd: ""
    }
  ]);
  const formIndex = ref<number>(0);
  const btnLoading = ref<boolean>(false);
  /*证书类型*/
  const certificateStatus = ref<boolean>(false);
  const certificateColumns = ref<System.Common.dictOptionArray>([]);
  const certificateValue = ref([]);
  /*证书等级*/
  const certificateLevelStatus = ref<boolean>(false);
  const certificateLevelColumns = ref<System.Common.dictOptionArray>([]);
  const certificateLevelValue = ref([]);
  // 证书时间
  const certificateTimeStatus = ref<boolean>(false);
  const certificateDate = ref<string[]>([
    String(dayjs().format("YYYY")),
    String(dayjs().format("MM"))
  ]);
  // 课程时间
  const showStartPicker = ref<boolean>(false);
  const currentStartDate = ref<string[]>([
    String(dayjs().format("YYYY")),
    String(dayjs().format("MM"))
  ]);
  const currentEndDate = ref<string[]>([
    String(dayjs().format("YYYY")),
    String(dayjs().format("MM"))
  ]);
  // 课程时间
  function openTrainTimePicker(index: number) {
    formIndex.value = index;
    showStartPicker.value = true;
    currentStartDate.value = [
      String(dayjs(formModel[formIndex.value].trainBegin || new Date()).format("YYYY")),
      String(dayjs(formModel[formIndex.value].trainBegin || new Date()).format("MM"))
    ];
    currentEndDate.value = [
      String(dayjs(formModel[formIndex.value].trainEnd || new Date()).format("YYYY")),
      String(dayjs(formModel[formIndex.value].trainEnd || new Date()).format("MM"))
    ];
  }
  function onConfirmStartTime() {
    const startDate = dayjs(currentStartDate.value).format("YYYYMM");
    const endDate = dayjs(currentEndDate.value).format("YYYYMM");
    if (endDate < startDate) {
      showFailToast("开始时间不能小于结束时间");
      return;
    }
    if (endDate === startDate) {
      showFailToast("开始时间不能与结束时间相同");
      return;
    }

    const formatDate = (arr: string[] | number[]) => {
      const [year, month] = arr;
      return `${year}-${String(month).padStart(2, "0")}`;
    };

    const start = formatDate(currentStartDate.value || []);
    const end = formatDate(currentEndDate.value || []);

    formModel[formIndex.value].trainTime = `${start} ~ ${end}`;
    formModel[formIndex.value].trainBegin = start;
    formModel[formIndex.value].trainEnd = end;
    showStartPicker.value = false;
  }

  /*证书等级*/
  function openCertificateLevelPicker(index: number) {
    try {
      formIndex.value = index;
      if (formModel[formIndex.value].certificateType === 1) {
        certificateLevelColumns.value = englishColumns.value;
      } else if (formModel[formIndex.value].certificateType === 2) {
        certificateLevelColumns.value = computerColumns.value;
      } else if (formModel[formIndex.value].certificateType === 3) {
        certificateLevelColumns.value = driverColumns.value;
      }
      certificateLevelStatus.value = true;
    } catch (e) {
      console.log(e);
    }
  }

  function onCertificateLevelConfirm({
    selectedValues,
    selectedOptions
  }: {
    selectedValues: number[];
    selectedOptions: { text: string; value: string | number }[];
  }) {
    formModel[formIndex.value].certificateLevelText = selectedOptions[0]?.text;
    formModel[formIndex.value].certificateLevel = selectedValues[0];
    if (formModel[formIndex.value].certificateType === 3) {
      formModel[formIndex.value].certificateName = selectedOptions[0]?.text;
    } else {
      formModel[formIndex.value].certificateName =
        formModel[formIndex.value].certificateTypeText + selectedOptions[0]?.text;
    }
    certificateLevelStatus.value = false;
  }

  /*证书类型*/
  function openCertificatePicker(index: number) {
    formIndex.value = index;
    certificateStatus.value = true;
    certificateValue.value = [formModel[formIndex.value].certificateType];
  }
  function onCertificateConfirm({
    selectedValues,
    selectedOptions
  }: {
    selectedValues: number[];
    selectedOptions: { text: string; value: string | number }[];
  }) {
    formModel[formIndex.value].certificateName = "";
    formModel[formIndex.value].certificateLevelText = "";
    formModel[formIndex.value].certificateLevel = null!;
    formModel[formIndex.value].certificateTypeText = selectedOptions[0]?.text;
    formModel[formIndex.value].certificateType = selectedValues[0];
    formModel[formIndex.value].certificateName = selectedOptions[0]?.text;
    formModel[formIndex.value].trainTime = "";
    if (selectedValues[0] === 11) {
      formModel[formIndex.value].certificateName = "";
    }
    certificateStatus.value = false;
  }
  // 证书获取时间
  function openCertificateTimePicker(index: number) {
    formIndex.value = index;
    certificateDate.value = [
      String(dayjs(formModel[formIndex.value].certificateTime || new Date()).format("YYYY")),
      String(dayjs(formModel[formIndex.value].certificateTime || new Date()).format("MM"))
    ];
    certificateTimeStatus.value = true;
  }
  function onCertificateTimeConfirm() {
    const formatDateString = (arr: string[]) => {
      const [year, month] = arr;
      return `${year}-${String(month).padStart(2, "0")}`;
    };
    formModel[formIndex.value].certificateTime = formatDateString(certificateDate.value);
    certificateTimeStatus.value = false;
  }
  function formatter(type: string, option: { text: string; value: string | number }) {
    if (type === "year") {
      option.text += "年";
    }
    if (type === "month") {
      option.text += "月";
    }
    return option;
  }
  function addRow() {
    formModel.push({
      certificateFile: [],
      certificateTime: null!,
      certificateLevel: null!,
      certificateLevelText: null!,
      certificateName: null!,
      certificateType: null!,
      certificateTypeText: null!,
      trainTime: "",
      trainBegin: "",
      trainEnd: "",
      trainName: null!
    });
  }
  function handleDel(index: number) {
    formModel.splice(index, 1);
  }
  // 获取字典
  async function getDictData() {
    const data = await getDictDataOptionsApi([
      "hrm_english_level",
      "hrm_driver_license",
      "hrm_certificate_type",
      "hrm_computer_level"
    ]);
    const newData = transformToTextValue(data);
    englishColumns.value = newData["hrm_english_level"];
    driverColumns.value = newData["hrm_driver_license"];
    certificateColumns.value = newData["hrm_certificate_type"];
    computerColumns.value = newData["hrm_computer_level"];
  }
  // 获取数据
  async function getData() {
    try {
      showLoadingToast({
        message: "正在获取信息...",
        forbidClick: true,
        loadingType: "spinner",
        duration: 0
      });
      const data = await getTrainCertificate(userInfo.value.id);
      for (const item of data) {
        item.certificateFile = item.certificateFileId
          ? [
              {
                url: getFileUrl(item.certificateFileId!),
                file: await urlToFile(getFileUrl(item.certificateFileId!)),
                isImage: true
              }
            ]
          : [];
        item.certificateTypeText =
          certificateColumns.value.find((i) => i.value === item.certificateType)?.text || "";
        if (item.certificateType === 1) {
          certificateLevelColumns.value = englishColumns.value;
        } else if (item.certificateType === 2) {
          certificateLevelColumns.value = computerColumns.value;
        } else if (item.certificateType === 3) {
          certificateLevelColumns.value = driverColumns.value;
        }
        item.certificateLevelText =
          certificateLevelColumns.value.find((i) => i.value === item.certificateLevel)?.text || "";
        item.trainTime = `${item.trainBegin} ~ ${item.trainEnd}`;
      }
      Object.assign(formModel, data);
    } catch (e) {
      console.log(e);
    } finally {
      closeToast();
    }
  }
  // 提交
  async function handleSubmit() {
    //  英语 驾照 计算机必须都存在
    const requiredTypes = [1, 2, 3]; // 英语、计算机、驾照
    const existingTypes = formModel.map((item) => Number(item.certificateType));
    const missingTypes = requiredTypes.filter((type) => !existingTypes.includes(type));

    if (missingTypes.length > 0) {
      showFailToast("请完整填写英语、计算机与驾照相关证书信息");
      return;
    }
    try {
      btnLoading.value = true;
      showLoadingToast({
        message: "正在提交信息...",
        forbidClick: true,
        loadingType: "spinner",
        duration: 0
      });
      const payload = deepClone(formModel);
      payload.forEach((item: Api.User.UpdateTrainingAndCertificatesReqVO) => {
        item.certificateFile = item.certificateFile?.[0]?.file;
      });
      await putTrainAndCertificates(userInfo.value.id, jsonToFormData({ details: payload }));
      infoStep.stepSetNext();
    } catch (e) {
      console.log(e);
    } finally {
      btnLoading.value = false;
      closeToast();
    }
  }
  onMounted(async () => {
    await getDictData();
    if (userInfo.value.id) await getData();
  });
</script>

<template>
  <div>
    <div class="w-full min-h-screen">
      <div class="mt-1">
        <van-form
          scroll-to-error
          scroll-to-error-position="nearest"
          label-width="70"
          @submit="handleSubmit"
        >
          <transition-group name="slide-in-fwd-center" tag="div">
            <div
              v-for="(item, index) in formModel"
              :key="index"
              class="border rounded-[10px] p-1.5 mb-3 relative"
            >
              <div class="text-lg font-bold ml-2">技能{{ index + 1 }}</div>
              <div v-if="index >= 1" class="absolute right-[-5px] top-[-10px]">
                <van-icon name="clear" color="red" size="large" @click="handleDel(index)" />
              </div>
              <van-cell-group>
                <van-field
                  v-model="item.certificateTypeText"
                  name="certificateTypeText"
                  label="证书类型"
                  placeholder="请选择证书类型"
                  :required="true"
                  is-link
                  readonly
                  :rules="[{ required: true, message: '请选择证书类型' }]"
                  @click="openCertificatePicker(index)"
                />

                <van-field
                  v-if="item.certificateType <= 3"
                  v-model="item.certificateLevelText"
                  name="certificateLevelText"
                  label="证书等级"
                  placeholder="请选择证书等级"
                  :required="true"
                  is-link
                  readonly
                  :rules="[{ required: true, message: '请选择证书等级' }]"
                  @click="item.certificateType && openCertificateLevelPicker(index)"
                />
                <van-field
                  v-model="item.certificateName"
                  name="certificateName"
                  placeholder="请输入证书名称"
                  label="证书名称"
                  :required="true"
                  :rules="[{ required: true, message: '请输入证书名称' }]"
                />
                <van-field
                  v-model="item.certificateTime"
                  readonly
                  is-link
                  name="certificateTime"
                  placeholder="请选择证书获取时间"
                  label-width="100"
                  label="证书获取时间"
                  @click="openCertificateTimePicker(index)"
                />
                <van-field name="certificateFile" label="证书照片" label-align="top">
                  <template #input>
                    <UploadImg v-model="item.certificateFile" :businessType="1" :kind="1" />
                  </template>
                </van-field>

                <van-field
                  v-if="![1, 2, 3].includes(item.certificateType)"
                  v-model="item.trainName"
                  name="time"
                  placeholder="请输入课程名称"
                  label="课程名称"
                />

                <van-field
                  v-if="![1, 2, 3].includes(item.certificateType)"
                  v-model="item.trainTime"
                  name="trainTime"
                  placeholder="请选择课程时间"
                  label="课程时间"
                  readonly
                  is-link
                  @click="openTrainTimePicker(index)"
                />
              </van-cell-group>
            </div>
          </transition-group>
          <div class="p-4">
            <van-button
              block
              icon="plus"
              round
              size="small"
              color="linear-gradient(90deg, #3b82f6 0%, #2563eb 100%)"
              @click="addRow"
            >
              增加一行
            </van-button>
          </div>
          <FixedButton :btnLoading="btnLoading" />
        </van-form>
      </div>
    </div>

    <!-- 时间 Picker -->
    <van-popup v-model:show="showStartPicker" round destroy-on-close position="bottom">
      <van-picker-group
        title="课程时间"
        :tabs="['开始时间', '结束时间']"
        @confirm="onConfirmStartTime"
        @cancel="showStartPicker = false"
      >
        <van-date-picker
          v-model="currentStartDate"
          :formatter="formatter"
          :columns-type="['year', 'month']"
          :min-date="new Date(2000, 1, 1)"
          :max-date="new Date()"
        />
        <van-date-picker
          v-model="currentEndDate"
          :formatter="formatter"
          :columns-type="['year', 'month']"
          :min-date="new Date(2000, 1, 1)"
          :max-date="new Date()"
        />
      </van-picker-group>
    </van-popup>
    <!--  证书获取时间  -->
    <van-popup v-model:show="certificateTimeStatus" round destroy-on-close position="bottom">
      <van-date-picker
        v-model="certificateDate"
        :formatter="formatter"
        :min-date="new Date(2000, 1, 1)"
        :max-date="new Date()"
        :columns-type="['year', 'month']"
        @confirm="onCertificateTimeConfirm"
        @cancel="certificateTimeStatus = false"
      />
    </van-popup>
    <!-- 证书类型 -->
    <van-popup v-model:show="certificateStatus" round destroy-on-close position="bottom">
      <van-picker
        v-model="certificateValue"
        :columns="certificateColumns"
        @confirm="onCertificateConfirm"
        @cancel="certificateStatus = false"
      />
    </van-popup>
    <!-- 证书等级 -->
    <van-popup v-model:show="certificateLevelStatus" round destroy-on-close position="bottom">
      <van-picker
        v-model="certificateLevelValue"
        :columns="certificateLevelColumns"
        @confirm="onCertificateLevelConfirm"
        @cancel="certificateLevelStatus = false"
      />
    </van-popup>
  </div>
</template>
<style scoped>
  .slide-in-fwd-center-enter-active,
  .slide-in-fwd-center-leave-active {
    transition: all 0.4s cubic-bezier(0.25, 1, 0.5, 1);
    will-change: transform, opacity;
  }

  .slide-in-fwd-center-enter-from {
    opacity: 0;
    transform: translateZ(-80px) scale(0.95);
  }

  .slide-in-fwd-center-leave-to {
    opacity: 0;
    transform: translateZ(-60px) scale(0.9);
  }

  :deep(.van-skeleton) {
    padding: 0 0;
  }
</style>
