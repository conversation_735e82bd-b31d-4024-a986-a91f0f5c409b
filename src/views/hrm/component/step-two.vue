<script setup lang="ts">
  import "vant/lib/toast/style";
  import { closeToast, showFailToast, showLoadingToast } from "vant";
  import dayjs from "dayjs";
  import { getDictDataOptionsApi } from "@/api/system";
  import { transformToTextValue } from "@/utils/common";
  import { deepClone } from "@/utils/deep-clone";
  import { educationExperience, getEducationExperience } from "@/api/hrm";
  import { jsonToFormData } from "@/utils/object-to-formData";
  import { getFileUrl, urlToFile } from "@/utils/get-file-url";

  const infoStep = useInfoStep();
  const { userInfo } = storeToRefs(infoStep);
  const columns = ref<System.Common.dictOptionArray>([]);
  const educationLevelColumns = ref<System.Common.dictOptionArray>([]);
  const pickerValue = ref();
  const pickerEducationValue = ref();
  const showPicker = ref<boolean>(false);
  const showEducationLevelPicker = ref<boolean>(false);
  const btnLoading = ref<boolean>(false);
  const showStartPicker = ref<boolean>(false);
  const currentStartDate = ref<string[]>([
    String(dayjs().format("YYYY")),
    String(dayjs().format("MM"))
  ]);
  const currentEndDate = ref<string[]>([
    String(dayjs().format("YYYY")),
    String(dayjs().format("MM"))
  ]);

  const formModel = reactive<Api.User.FormItem[]>([
    {
      time: "",
      begin: "",
      end: "",
      name: "",
      major: "",
      status: null!,
      educationStatusText: "",
      educationLevel: null!,
      educationLevelText: "",
      proofDocument: [],
      badgePhoto1: [],
      badgePhoto2: [],
      badgePhoto3: []
    }
  ]);
  const indexType = reactive<{
    index: number;
    type: string;
  }>({
    index: 0,
    type: "startTime"
  });

  function addRow() {
    formModel.push({
      time: "",
      begin: "",
      end: "",
      name: "",
      major: "",
      status: null!,
      educationStatusText: "",
      educationLevel: null!,
      educationLevelText: "",
      proofDocument: [],
      badgePhoto1: [],
      badgePhoto2: [],
      badgePhoto3: []
    });
  }

  function formatter(type: string, option: { text: string; value: string | number }) {
    if (type === "year") {
      option.text += "年";
    }
    if (type === "month") {
      option.text += "月";
    }
    return option;
  }
  // 学历状态确认
  function onConfirm({
    selectedValues,
    selectedOptions
  }: {
    selectedValues: number[];
    selectedOptions: { text: string; value: string | number }[];
  }) {
    formModel[indexType.index].educationStatusText = selectedOptions[0]?.text;
    formModel[indexType.index].status = selectedValues[0];
    showPicker.value = false;
  }

  // 学历确认
  function onEducationConfirm({
    selectedValues,
    selectedOptions
  }: {
    selectedValues: number[];
    selectedOptions: { text: string; value: string | number }[];
  }) {
    formModel[indexType.index].educationLevelText = selectedOptions[0]?.text;
    formModel[indexType.index].educationLevel = selectedValues[0];
    showEducationLevelPicker.value = false;
  }

  function openPicker(index: number, type: "startTime" | "endTime") {
    indexType.index = index;
    indexType.type = type;
    showStartPicker.value = true;
    currentStartDate.value = [
      String(dayjs(formModel[indexType.index].begin || new Date()).format("YYYY")),
      String(dayjs(formModel[indexType.index].begin || new Date()).format("MM"))
    ];
    currentEndDate.value = [
      String(dayjs(formModel[indexType.index].end || new Date()).format("YYYY")),
      String(dayjs(formModel[indexType.index].end || new Date()).format("MM"))
    ];
  }

  function openEducationPicker(index: number, type: "educationLevelText" | "educationStatusText") {
    indexType.index = index;
    indexType.type = type;
    if (type === "educationStatusText") {
      showPicker.value = true;
      pickerValue.value = [formModel[indexType.index].status];
    } else {
      showEducationLevelPicker.value = true;
      pickerEducationValue.value = [formModel[indexType.index].educationLevel];
    }
  }

  // 时间选择逻辑
  function onConfirmStartTime() {
    const { index } = indexType;

    const formatDateString = (arr: string[]) => {
      const [year, month] = arr;
      return `${year}-${String(month).padStart(2, "0")}`;
    };

    const startStr = formatDateString(currentStartDate.value);
    const endStr = formatDateString(currentEndDate.value);

    const startDate = dayjs(startStr, "YYYY-MM");
    const endDate = dayjs(endStr, "YYYY-MM");

    if (!startDate.isValid() || !endDate.isValid()) {
      showFailToast("日期格式非法");
      return;
    }

    if (endDate.isBefore(startDate)) {
      showFailToast("开始时间不能小于结束时间");
      return;
    }

    if (endDate.isSame(startDate)) {
      showFailToast("开始时间不能与结束时间相同");
      return;
    }

    formModel[index].time = `${startStr} ~ ${endStr}`;
    formModel[index].begin = startStr;
    formModel[index].end = endStr;
    showStartPicker.value = false;
  }
  function handleDel(index: number) {
    formModel.splice(index, 1);
  }
  // 提交数据
  async function handleSubmit() {
    try {
      btnLoading.value = true;
      showLoadingToast({
        message: "正在提交信息...",
        forbidClick: true,
        loadingType: "spinner",
        duration: 0
      });
      const payload = deepClone(formModel) || [];
      const newPayload = (Array.isArray(payload) ? payload : []).map((item: Api.User.FormItem) => {
        const { badgePhoto1 = [], badgePhoto2 = [], badgePhoto3 = [], ...rest } = item || {};

        const proofDocument = [...badgePhoto1, ...badgePhoto2, ...badgePhoto3].filter(Boolean);

        return {
          ...rest,
          proofDocument
        };
      });

      await educationExperience(userInfo.value.id, jsonToFormData({ details: newPayload }));
      infoStep.stepSetNext();
    } catch (e) {
      console.log(e);
    } finally {
      btnLoading.value = false;
      closeToast();
    }
  }
  // 获取字典
  async function getDictData() {
    const data = await getDictDataOptionsApi(["hrm_education_level_type", "hrm_education_type"]);
    const newData = transformToTextValue(data);
    columns.value = newData["hrm_education_type"];
    educationLevelColumns.value = newData["hrm_education_level_type"];
  }
  // 获取数据
  async function getData() {
    try {
      showLoadingToast({
        message: "正在获取信息...",
        forbidClick: true,
        loadingType: "spinner",
        duration: 0
      });

      const data = await getEducationExperience(userInfo.value.id);
      const getBadgePhotos = async (docs: any[], kind: number) => {
        const filtered = Array.isArray(docs) ? docs.filter((i) => i?.kind === kind) : [];
        return await Promise.all(
          filtered.map(async (doc) => ({
            url: getFileUrl(doc.fileId),
            file: await urlToFile(getFileUrl(doc.fileId)),
            isImage: true,
            businessType: 1,
            kind
          }))
        );
      };

      const newForm = await Promise.all(
        (Array.isArray(data) ? data : []).map(async (item: Api.User.FormItem) => {
          const { proofDocument, begin, end, name, major, status, educationLevel } = item;

          const [badgePhoto1, badgePhoto2, badgePhoto3] = await Promise.all([
            getBadgePhotos(proofDocument, 13),
            getBadgePhotos(proofDocument, 14),
            getBadgePhotos(proofDocument, 12)
          ]);

          return {
            time: `${begin} ~ ${end}`,
            begin,
            end,
            name,
            major,
            status,
            educationStatusText: columns.value.find((i) => i.value === status)?.text || "",
            educationLevel,
            educationLevelText:
              educationLevelColumns.value.find((i) => i.value === educationLevel)?.text || "",
            badgePhoto1,
            badgePhoto2,
            badgePhoto3
          };
        })
      );

      Object.assign(formModel, newForm);
    } catch (e) {
      console.error("获取教育经历失败", e);
    } finally {
      closeToast();
    }
  }

  onMounted(async () => {
    await getDictData();
    if (userInfo.value.id) await getData();
  });
</script>

<template>
  <div>
    <div class="w-full min-h-screen">
      <div>
        <van-form
          scroll-to-error
          scroll-to-error-position="nearest"
          label-width="70"
          class="mt-[10px]"
          @submit="handleSubmit"
        >
          <transition-group name="slide-in-fwd-center" tag="div">
            <div
              v-for="(item, index) in formModel"
              :key="index"
              class="border rounded-[10px] p-1.5 mb-3 relative"
            >
              <div class="text-lg font-bold ml-2">教育经历{{ index + 1 }}</div>
              <div v-if="index >= 1" class="absolute right-[-5px] top-[-10px]">
                <van-icon name="clear" color="red" size="large" @click="handleDel(index)" />
              </div>
              <van-cell-group>
                <van-field
                  v-model="item.time"
                  readonly
                  name="time"
                  placeholder="请选择时间"
                  label="在校时间"
                  :required="true"
                  is-link
                  :rules="[{ required: true, message: '请选择时间' }]"
                  @click="openPicker(index, 'startTime')"
                />

                <van-field
                  v-model="item.educationLevelText"
                  name="educationLevelText"
                  label="学历"
                  placeholder="请选择学历"
                  :required="true"
                  is-link
                  readonly
                  :rules="[{ required: true, message: '请选择学历' }]"
                  @click="openEducationPicker(index, 'educationLevelText')"
                />
                <van-field
                  v-if="item.educationLevel >= 3"
                  v-model="item.major"
                  name="major"
                  label="专业"
                  placeholder="请输入专业"
                  :required="true"
                  :rules="[{ required: true, message: '请输入专业' }]"
                />

                <van-field
                  v-model="item.educationStatusText"
                  name="educationStatusText"
                  label="学历状态"
                  placeholder="请选择学历状态"
                  :required="true"
                  is-link
                  readonly
                  :rules="[{ required: true, message: '请选择学历状态' }]"
                  @click="openEducationPicker(index, 'educationStatusText')"
                />
                <van-field
                  v-model="item.name"
                  name="name"
                  label="就读学校"
                  placeholder="请输入就读学校"
                  :required="true"
                  :rules="[{ required: true, message: '请输入就读学校' }]"
                />

                <van-row v-if="item.educationLevel >= 3">
                  <van-col :span="8">
                    <van-field
                      name="badgePhoto3"
                      label="学信网截图"
                      label-width="80"
                      label-align="top"
                      :required="true"
                      class="form_text"
                      :rules="[{ required: true, message: '请上传学信网截图' }]"
                    >
                      <template #input>
                        <UploadImg v-model="item.badgePhoto3" :businessType="1" :kind="12" />
                      </template>
                    </van-field>
                  </van-col>
                  <van-col :span="8">
                    <van-field
                      name="badgePhoto1"
                      label="学历证明"
                      label-align="top"
                      :required="Array.isArray(item.badgePhoto3) && item.badgePhoto3.length === 0"
                      class="form_text"
                      :rules="Array.isArray(item.badgePhoto3) && item.badgePhoto3.length === 0 ? [{ required: true, message: '请上传学历证明' }] : []"
                    >
                      <template #input>
                        <UploadImg v-model="item.badgePhoto1" :businessType="1" :kind="13" />
                      </template>
                    </van-field>
                  </van-col>
                  <van-col :span="8">
                    <van-field
                      v-if="item.educationLevel > 3"
                      name="badgePhoto2"
                      label="学位证明"
                      label-align="top"
                      :required="Array.isArray(item.badgePhoto3) && item.badgePhoto3.length === 0"
                      class="form_text"
                      :rules="Array.isArray(item.badgePhoto3) && item.badgePhoto3.length === 0 ? [{ required: true, message: '请上传学位证明' }] : []"
                    >
                      <template #input>
                        <UploadImg v-model="item.badgePhoto2" :businessType="1" :kind="14" />
                      </template>
                    </van-field>
                  </van-col>

                  <div class="text-xs text-gray-500">
                    请提供最高学历及学位证明（如有）的扫描件，或使用学信网认证截图。为便于上传，推荐优先选择学信网截图。
                  </div>
                </van-row>
              </van-cell-group>
            </div>
          </transition-group>
          <div class="p-4">
            <van-button
              block
              round
              size="small"
              color="linear-gradient(90deg, #3b82f6 0%, #2563eb 100%)"
              icon="plus"
              @click="addRow"
            >
              增加教育经历
            </van-button>
          </div>
          <FixedButton :btnLoading="btnLoading" />
        </van-form>
      </div>
    </div>

    <!-- 时间 Picker -->
    <van-popup v-model:show="showStartPicker" round destroy-on-close position="bottom">
      <van-picker-group
        title="在校时间"
        :tabs="['开始时间', '结束时间']"
        @confirm="onConfirmStartTime"
        @cancel="showStartPicker = false"
      >
        <van-date-picker
          v-model="currentStartDate"
          :formatter="formatter"
          :min-date="new Date(2000, 1, 1)"
          :max-date="new Date()"
          :columns-type="['year', 'month']"
        />
        <van-date-picker
          v-model="currentEndDate"
          :formatter="formatter"
          :min-date="new Date(2000, 1, 1)"
          :max-date="new Date()"
          :columns-type="['year', 'month']"
        />
      </van-picker-group>
    </van-popup>
    <!-- 学历状态 -->
    <van-popup v-model:show="showPicker" round destroy-on-close position="bottom">
      <van-picker
        v-model="pickerValue"
        :columns="columns"
        @confirm="onConfirm"
        @cancel="showPicker = false"
      />
    </van-popup>
    <!-- 学历 -->
    <van-popup v-model:show="showEducationLevelPicker" round destroy-on-close position="bottom">
      <van-picker
        v-model="pickerEducationValue"
        :columns="educationLevelColumns"
        @confirm="onEducationConfirm"
        @cancel="showEducationLevelPicker = false"
      />
    </van-popup>
  </div>
</template>
<style scoped>
  .slide-in-fwd-center-enter-active,
  .slide-in-fwd-center-leave-active {
    transition: all 0.4s cubic-bezier(0.25, 1, 0.5, 1);
    will-change: transform, opacity;
  }

  .slide-in-fwd-center-enter-from {
    opacity: 0;
    transform: translateZ(-80px) scale(0.95);
  }

  .slide-in-fwd-center-leave-to {
    opacity: 0;
    transform: translateZ(-60px) scale(0.9);
  }

  :deep(.form_text) {
    .van-field__label {
      font-size: 12px;
      font-weight: 500;
    }
  }
  :deep(.van-skeleton) {
    padding: 0 0;
  }
</style>
